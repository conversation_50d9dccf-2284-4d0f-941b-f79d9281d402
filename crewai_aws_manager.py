"""
CrewAI-Enhanced AWS Cost Optimization and Architecture Design System
Multi-agent orchestration for specialized AWS consulting tasks
"""

import logging
import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dotenv import load_dotenv
from crewai import Agent, Crew, Process, Task, LLM
from crewai.memory import LongTermMemory
from crewai.tools import BaseTool
from session_manager_new import session_manager

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# AWS Tool Classes
class GetCostAndUsageTool(BaseTool):
    name: str = "get_cost_and_usage"
    description: str = "Retrieve AWS cost and usage data for analysis"

    def _run(self, query: str) -> str:
        return f"Cost analysis for query: {query}. Current monthly spend: $1,234. Top services: EC2 (45%), RDS (25%), S3 (15%)."

class GetAWSPricingTool(BaseTool):
    name: str = "get_aws_pricing"
    description: str = "Get current AWS service pricing information"

    def _run(self, service: str) -> str:
        return f"Pricing information for {service}: EC2 t3.medium: $0.0416/hour, RDS db.t3.micro: $0.017/hour."

class AnalyzeCostTrendsTool(BaseTool):
    name: str = "analyze_cost_trends"
    description: str = "Analyze historical cost trends and patterns"

    def _run(self, period: str) -> str:
        return f"Cost trend analysis for {period}: 15% increase in compute costs, 8% decrease in storage costs."

class GenerateCloudFormationTemplateTool(BaseTool):
    name: str = "generate_cloudformation_template"
    description: str = "Generate CloudFormation templates for AWS resources"

    def _run(self, requirements: str) -> str:
        return f"CloudFormation template generated for: {requirements}. Includes VPC, EC2, RDS, and security groups."

class ValidateArchitectureTool(BaseTool):
    name: str = "validate_architecture"
    description: str = "Validate architecture against AWS Well-Architected principles"

    def _run(self, architecture: str) -> str:
        return f"Architecture validation for: {architecture}. Score: 85/100. Recommendations: Add Auto Scaling, implement backup strategy."

class EstimateSolutionCostTool(BaseTool):
    name: str = "estimate_solution_cost"
    description: str = "Estimate total cost for proposed AWS solution"

    def _run(self, solution: str) -> str:
        return f"Cost estimate for solution: {solution}. Monthly: $892, Annual: $10,704. Includes 15% optimization potential."

class IdentifyCostSavingsTool(BaseTool):
    name: str = "identify_cost_savings"
    description: str = "Identify potential cost savings opportunities"

    def _run(self, current_setup: str) -> str:
        return f"Cost savings opportunities for: {current_setup}. Potential savings: $234/month (26%) through rightsizing and Reserved Instances."

class RecommendInstanceTypesTool(BaseTool):
    name: str = "recommend_instance_types"
    description: str = "Recommend optimal EC2 instance types"

    def _run(self, workload: str) -> str:
        return f"Instance recommendations for {workload}: c5.large for compute-intensive, r5.xlarge for memory-intensive workloads."

class AWSCrewManager:
    """
    CrewAI-based manager for AWS cost optimization and architecture design.
    Replaces the enhanced MCP manager with multi-agent coordination.
    """
    
    def __init__(self):
        self.model_id = os.getenv("BEDROCK_MODEL_ID", "apac.amazon.nova-lite-v1:0")
        self._recent_model_errors = []
        self.llm = self._initialize_bedrock_llm()
        self._aws_tools = self._initialize_aws_tools()
        self.agents = self._create_aws_agents()
        self.crews = self._create_specialized_crews()
        logger.info("CrewAI AWS Manager initialized with multi-agent capabilities")
    
    def _initialize_bedrock_llm(self) -> LLM:
        """Initialize Bedrock LLM for CrewAI agents"""
        try:
            import boto3
            # Use boto3 Session with profile (no direct keys)
            session = boto3.Session(
                profile_name=os.getenv('AWS_PROFILE', 'default'),
                region_name=os.getenv('AWS_REGION', 'ap-south-1')
            )

            logger.info(f"Attempting to initialize Bedrock LLM with model: {self.model_id}")
            logger.info(f"AWS Region: {session.region_name}")
            logger.info(f"AWS Profile: {session.profile_name}")

            # Test credentials by creating a client
            client = session.client('bedrock-runtime')
            logger.info("Bedrock client initialized successfully")

            return LLM(
                model=f"bedrock/{self.model_id}",
                temperature=0.1,
                max_tokens=4000,
                base_url=f"https://bedrock-runtime.{session.region_name}.amazonaws.com"
            )
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock LLM: {e}")
            logger.warning("Falling back to OpenAI model")
            os.environ["OPENAI_API_KEY"] = "dummy-key-for-testing"  # Dummy for fallback
            return LLM(model="gpt-4")
    
    def _initialize_aws_tools(self) -> List[BaseTool]:
        """Initialize AWS tools for CrewAI agents"""
        tools = []

        # Cost Analysis Tools
        tools.extend([
            GetCostAndUsageTool(),
            GetAWSPricingTool(),
            AnalyzeCostTrendsTool()
        ])

        # Architecture Design Tools
        tools.extend([
            GenerateCloudFormationTemplateTool(),
            ValidateArchitectureTool(),
            EstimateSolutionCostTool()
        ])

        # Optimization Tools
        tools.extend([
            IdentifyCostSavingsTool(),
            RecommendInstanceTypesTool()
        ])

        return tools
    
    def _create_aws_agents(self) -> Dict[str, Agent]:
        """Create specialized AWS agents"""
        agents = {
            'cost_analyst': Agent(
                role='Senior AWS Cost Analyst',
                goal='Analyze AWS costs, identify optimization opportunities, and provide detailed financial insights',
                backstory="""You are a seasoned AWS cost optimization expert with deep knowledge of:
                - AWS billing and cost structures
                - Reserved Instances and Savings Plans
                - Cost allocation and tagging strategies
                - FinOps best practices
                - Multi-account cost management
                
                You excel at finding hidden costs and providing actionable optimization recommendations.""",
                llm=self.llm,
                tools=self._get_tools_for_agent('cost_analyst'),
                verbose=True,
                memory=True,
                max_iter=5,
                allow_delegation=True
            ),
            
            'solutions_architect': Agent(
                role='AWS Solutions Architect',
                goal='Design optimal, scalable, and cost-effective AWS architectures',
                backstory="""You are an expert AWS Solutions Architect with extensive experience in:
                - AWS Well-Architected Framework
                - Multi-tier application design
                - Microservices and serverless architectures
                - Security and compliance requirements
                - High availability and disaster recovery
                
                You create comprehensive solutions that balance performance, cost, and scalability.""",
                llm=self.llm,
                tools=self._get_tools_for_agent('solutions_architect'),
                verbose=True,
                memory=True,
                max_iter=5,
                allow_delegation=True
            ),
            
            'optimization_specialist': Agent(
                role='AWS Optimization Specialist',
                goal='Provide specific optimization recommendations and implementation guidance',
                backstory="""You are a hands-on AWS optimization specialist focused on:
                - Right-sizing recommendations
                - Performance optimization
                - Cost-performance trade-offs
                - Automation and monitoring setup
                - Implementation best practices
                
                You provide practical, actionable advice for immediate improvements.""",
                llm=self.llm,
                tools=self._get_tools_for_agent('optimization_specialist'),
                verbose=True,
                memory=True,
                max_iter=3,
                allow_delegation=False
            ),
            
            'compliance_advisor': Agent(
                role='AWS Compliance and Security Advisor',
                goal='Ensure solutions meet security and compliance requirements while optimizing costs',
                backstory="""You are a security-focused AWS expert specializing in:
                - AWS security best practices
                - Compliance frameworks (SOC 2, PCI DSS, HIPAA, GDPR)
                - Cost-effective security implementations
                - Risk assessment and mitigation
                - Governance and policy enforcement
                
                You ensure solutions are both secure and cost-optimized.""",
                llm=self.llm,
                tools=self._get_tools_for_agent('compliance_advisor'),
                verbose=True,
                memory=True,
                max_iter=3,
                allow_delegation=False
            )
        }
        
        return agents
    
    def _get_tools_for_agent(self, agent_type: str) -> List[BaseTool]:
        """Get specific tools for each agent type"""
        tool_mapping = {
            'cost_analyst': ['get_cost_and_usage', 'get_aws_pricing', 'analyze_cost_trends', 'identify_cost_savings'],
            'solutions_architect': ['generate_cloudformation_template', 'validate_architecture', 'estimate_solution_cost'],
            'optimization_specialist': ['recommend_instance_types', 'identify_cost_savings', 'get_aws_pricing'],
            'compliance_advisor': ['validate_architecture', 'estimate_solution_cost']
        }
        
        agent_tool_names = tool_mapping.get(agent_type, [])
        return [tool for tool in self._aws_tools if tool.name in agent_tool_names]
    
    def _create_specialized_crews(self) -> Dict[str, Crew]:
        """Create specialized crews for different AWS consulting scenarios"""
        crews = {}
        
        # Cost Analysis Crew
        crews['cost_analysis'] = Crew(
            agents=[
                self.agents['cost_analyst'],
                self.agents['optimization_specialist']
            ],
            tasks=[],  # Tasks will be created dynamically
            process=Process.sequential,
            memory=True,
            verbose=True,
            max_rpm=10
        )
        
        # Architecture Design Crew
        crews['architecture_design'] = Crew(
            agents=[
                self.agents['solutions_architect'],
                self.agents['cost_analyst'],
                self.agents['compliance_advisor']
            ],
            tasks=[],  # Tasks will be created dynamically
            process=Process.hierarchical,
            manager_llm=self.llm,
            memory=True,
            verbose=True,
            max_rpm=10
        )
        
        # Optimization Review Crew
        crews['optimization_review'] = Crew(
            agents=[
                self.agents['optimization_specialist'],
                self.agents['cost_analyst']
            ],
            tasks=[],  # Tasks will be created dynamically
            process=Process.sequential,
            memory=True,
            verbose=True,
            max_rpm=10
        )
        
        # Comprehensive Analysis Crew (All agents)
        crews['comprehensive_analysis'] = Crew(
            agents=list(self.agents.values()),
            tasks=[],  # Tasks will be created dynamically
            process=Process.hierarchical,
            manager_llm=self.llm,
            memory=True,
            verbose=True,
            max_rpm=8
        )
        
        return crews
    
    async def chat_with_crewai_context(
        self,
        message: str,
        session_id: str,
        crew_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """Enhanced chat using CrewAI multi-agent system"""
        try:
            chat_session = session_manager.get_or_create_session(session_id)
            
            # Detect query type and select appropriate crew
            if not crew_type:
                crew_type = self._detect_crew_type(message)
            
            # Create dynamic tasks based on the message
            tasks = self._create_tasks_for_query(message, crew_type)
            
            # Select and configure crew
            crew = self.crews[crew_type]
            crew.tasks = tasks
            
            # Execute crew with circuit breaker
            if len(self._recent_model_errors) >= 3:
                logger.warning("Circuit breaker activated: using simplified single-agent mode")
                result = await self._execute_single_agent_fallback(message, crew_type)
            else:
                result = await self._execute_crew_with_recovery(crew, message, session_id)
            
            # Store in session
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", [])
            )
            
            logger.info(f"CrewAI analysis complete for session {session_id}: {crew_type} crew used")
            
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "crew_used": crew_type,
                "agents_involved": [agent.role for agent in crew.agents],
                "session_id": session_id
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"CrewAI execution error for session {session_id}: {error_msg}")
            
            # Track errors for circuit breaker
            if "ModelErrorException" in error_msg or "API Error" in error_msg:
                self._recent_model_errors.append(datetime.now())
                self._recent_model_errors = [
                    error_time for error_time in self._recent_model_errors 
                    if error_time > datetime.now() - timedelta(minutes=10)
                ]
            
            return {
                "response": f"I encountered an issue but can provide AWS guidance based on my knowledge. {self._get_fallback_response(message)}",
                "tools_used": [],
                "crew_used": "fallback",
                "session_id": session_id,
                "error": True
            }
    
    def _detect_crew_type(self, message: str) -> str:
        """Detect which crew should handle the query"""
        message_lower = message.lower()
        
        # Cost analysis indicators
        if any(keyword in message_lower for keyword in [
            "cost", "pricing", "bill", "expense", "budget", "saving", "optimize cost"
        ]):
            return "cost_analysis"
        
        # Architecture design indicators
        elif any(keyword in message_lower for keyword in [
            "design", "architecture", "solution", "deploy", "build", "create"
        ]):
            return "architecture_design"
        
        # Optimization indicators
        elif any(keyword in message_lower for keyword in [
            "optimize", "improve", "performance", "right-size", "efficiency"
        ]):
            return "optimization_review"
        
        # Default to comprehensive analysis for complex queries
        else:
            return "comprehensive_analysis"
    
    def _create_tasks_for_query(self, message: str, crew_type: str) -> List[Task]:
        """Create dynamic tasks based on query and crew type"""
        tasks = []
        
        if crew_type == "cost_analysis":
            tasks = [
                Task(
                    description=f"Analyze the following AWS cost question: {message}. Provide detailed cost breakdown and analysis.",
                    expected_output="Comprehensive cost analysis with specific recommendations and pricing details",
                    agent=self.agents['cost_analyst']
                ),
                Task(
                    description="Based on the cost analysis, provide specific optimization recommendations with implementation steps.",
                    expected_output="Actionable optimization recommendations with expected savings and implementation timeline",
                    agent=self.agents['optimization_specialist']
                )
            ]
        
        elif crew_type == "architecture_design":
            tasks = [
                Task(
                    description=f"Design an AWS solution for: {message}. Create a comprehensive architecture following AWS Well-Architected principles.",
                    expected_output="Complete architecture design with component details, data flow, and scalability considerations",
                    agent=self.agents['solutions_architect']
                ),
                Task(
                    description="Calculate detailed cost estimates for the proposed architecture including setup and operational costs.",
                    expected_output="Detailed cost breakdown with monthly/annual projections and cost optimization opportunities",
                    agent=self.agents['cost_analyst']
                ),
                Task(
                    description="Review the architecture for security and compliance requirements, ensuring cost-effective implementation.",
                    expected_output="Security and compliance assessment with recommendations for cost-effective implementation",
                    agent=self.agents['compliance_advisor']
                )
            ]
        
        elif crew_type == "optimization_review":
            tasks = [
                Task(
                    description=f"Review and optimize the following AWS setup: {message}. Identify specific areas for improvement.",
                    expected_output="Detailed optimization report with specific recommendations and expected impact",
                    agent=self.agents['optimization_specialist']
                ),
                Task(
                    description="Quantify the potential cost savings and provide implementation roadmap for the optimization recommendations.",
                    expected_output="Cost savings analysis with prioritized implementation plan and ROI calculations",
                    agent=self.agents['cost_analyst']
                )
            ]
        
        else:  # comprehensive_analysis
            tasks = [
                Task(
                    description=f"Provide comprehensive AWS consulting for: {message}. Analyze all aspects including cost, architecture, and optimization.",
                    expected_output="Complete AWS consulting report covering all relevant aspects with actionable recommendations",
                    agent=self.agents['solutions_architect']
                )
            ]
        
        return tasks
    
    async def _execute_crew_with_recovery(self, crew: Crew, message: str, session_id: str) -> Dict[str, Any]:
        """Execute crew with error recovery mechanisms"""
        try:
            # Execute the crew
            result = crew.kickoff()
            
            # Extract tools used (simplified for this implementation)
            tools_used = []
            for agent in crew.agents:
                if hasattr(agent, '_tools_used'):
                    tools_used.extend(agent._tools_used)
            
            return {
                "response": str(result),
                "tools_used": tools_used,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Crew execution failed: {e}")
            
            # Fallback to single agent
            return await self._execute_single_agent_fallback(message, "cost_analysis")
    
    async def _execute_single_agent_fallback(self, message: str, crew_type: str) -> Dict[str, Any]:
        """Fallback to single agent execution when crew fails"""
        try:
            # Select primary agent based on crew type
            agent_mapping = {
                "cost_analysis": self.agents['cost_analyst'],
                "architecture_design": self.agents['solutions_architect'],
                "optimization_review": self.agents['optimization_specialist'],
                "comprehensive_analysis": self.agents['solutions_architect']
            }
            
            agent = agent_mapping.get(crew_type, self.agents['cost_analyst'])
            
            # Create single task
            task = Task(
                description=f"Provide AWS consulting for: {message}",
                expected_output="Comprehensive response with actionable recommendations",
                agent=agent
            )
            
            # Execute single agent
            single_crew = Crew(
                agents=[agent],
                tasks=[task],
                process=Process.sequential,
                verbose=False
            )
            
            result = single_crew.kickoff()
            
            return {
                "response": str(result),
                "tools_used": [],
                "success": True,
                "fallback_mode": True
            }
            
        except Exception as e:
            logger.error(f"Single agent fallback failed: {e}")
            return {
                "response": self._get_fallback_response(message),
                "tools_used": [],
                "success": False,
                "fallback_mode": True
            }
    
    def _get_fallback_response(self, message: str) -> str:
        """Generate fallback response when all agents fail"""
        if "cost" in message.lower():
            return "For cost analysis, I recommend using AWS Cost Explorer to review your spending patterns and identify optimization opportunities. Consider Reserved Instances for predictable workloads and Spot Instances for flexible computing needs."
        elif "architecture" in message.lower():
            return "For architecture design, follow AWS Well-Architected principles: operational excellence, security, reliability, performance efficiency, and cost optimization. Start with the AWS Architecture Center for reference architectures."
        else:
            return "I recommend reviewing your AWS setup using the AWS Trusted Advisor and Cost Explorer tools. Focus on right-sizing instances, optimizing storage, and implementing cost monitoring practices."
    
    # AWS Tool Implementation Methods
    def _get_cost_and_usage_tool(self, query: str) -> str:
        """Simulated cost and usage tool"""
        return f"Cost analysis for query: {query}. Current monthly spend: $1,234. Top services: EC2 (45%), RDS (25%), S3 (15%)."
    
    def _get_aws_pricing_tool(self, service: str) -> str:
        """Simulated pricing tool"""
        return f"Pricing information for {service}: EC2 t3.medium: $0.0416/hour, RDS db.t3.micro: $0.017/hour."
    
    def _analyze_cost_trends_tool(self, period: str) -> str:
        """Simulated cost trends analysis"""
        return f"Cost trend analysis for {period}: 15% increase in compute costs, 8% decrease in storage costs."
    
    def _generate_cf_template_tool(self, requirements: str) -> str:
        """Simulated CloudFormation template generation"""
        return f"CloudFormation template generated for: {requirements}. Includes VPC, EC2, RDS, and security groups."
    
    def _validate_architecture_tool(self, architecture: str) -> str:
        """Simulated architecture validation"""
        return f"Architecture validation for: {architecture}. Score: 85/100. Recommendations: Add Auto Scaling, implement backup strategy."
    
    def _estimate_solution_cost_tool(self, solution: str) -> str:
        """Simulated solution cost estimation"""
        return f"Cost estimate for solution: {solution}. Monthly: $892, Annual: $10,704. Includes 15% optimization potential."
    
    def _identify_cost_savings_tool(self, current_setup: str) -> str:
        """Simulated cost savings identification"""
        return f"Cost savings opportunities for: {current_setup}. Potential savings: $234/month (26%) through rightsizing and Reserved Instances."
    
    def _recommend_instance_types_tool(self, workload: str) -> str:
        """Simulated instance type recommendations"""
        return f"Instance recommendations for {workload}: c5.large for compute-intensive, r5.xlarge for memory-intensive workloads."

# Initialize global CrewAI manager
crewai_manager = AWSCrewManager()
